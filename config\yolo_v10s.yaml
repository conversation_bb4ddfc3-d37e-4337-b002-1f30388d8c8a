# YOLOv10s模型配置文件
# 用于车牌检测的YOLOv10小型模型配置

# 模型基本信息
model_info:
  name: "YOLOv10s"
  description: "YOLOv10 small model for license plate detection"
  version: "1.0"
  input_size: [960, 960]
  channels: 3

# 网络架构参数
architecture:
  # 骨干网络
  backbone:
    type: "CSPDarknet"
    depth_multiple: 0.33
    width_multiple: 0.50
    
  # 颈部网络
  neck:
    type: "PAFPN"
    
  # 检测头
  head:
    type: "Detect"
    nc: 1  # 类别数
    anchors: 3  # 每个网格的锚点数

# 训练超参数
training:
  # 基础学习率
  lr0: 0.01
  
  # 最终学习率因子
  lrf: 0.01
  
  # 动量
  momentum: 0.937
  
  # 权重衰减
  weight_decay: 0.0005
  
  # 预热轮数
  warmup_epochs: 3.0
  
  # 预热动量
  warmup_momentum: 0.8
  
  # 预热偏置学习率
  warmup_bias_lr: 0.1
  
  # 损失函数权重
  loss_weights:
    box: 7.5      # 边界框损失
    cls: 0.5      # 分类损失
    dfl: 1.5      # DFL损失
    
  # 标签平滑
  label_smoothing: 0.0
  
  # 标准批次大小
  nbs: 64
  
  # 梯度累积
  accumulate: true

# 数据增强
augmentation:
  # HSV增强
  hsv_h: 0.015    # 色调
  hsv_s: 0.7      # 饱和度
  hsv_v: 0.4      # 明度
  
  # 几何变换
  degrees: 0.0      # 旋转角度
  translate: 0.1    # 平移
  scale: 0.5        # 缩放
  shear: 0.0        # 剪切
  perspective: 0.0  # 透视变换
  
  # 翻转
  flipud: 0.0       # 上下翻转概率
  fliplr: 0.5       # 左右翻转概率
  
  # 混合增强
  mosaic: 1.0       # Mosaic增强概率
  mixup: 0.0        # MixUp增强概率
  copy_paste: 0.0   # 复制粘贴增强概率

# 优化器配置
optimizer:
  type: "SGD"
  
  # SGD参数
  sgd:
    momentum: 0.937
    nesterov: true
    
  # Adam参数
  adam:
    betas: [0.9, 0.999]
    eps: 1e-8
    
  # AdamW参数
  adamw:
    betas: [0.9, 0.999]
    eps: 1e-8

# 学习率调度器
scheduler:
  type: "cosine"
  
  # 余弦退火
  cosine:
    T_max: 150
    eta_min: 0.0001
    
  # 步长调度
  step:
    step_size: 50
    gamma: 0.1
    
  # 多步调度
  multistep:
    milestones: [100, 130]
    gamma: 0.1

# 模型结构定义
# 格式: [from, number, module, args]
backbone:
  # Stem
  - [-1, 1, Conv, [64, 6, 2, 2]]  # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]]    # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]]    # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, Conv, [512, 3, 2]]    # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]]   # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]]      # 9

# 检测头
head:
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
  - [[-1, 6], 1, Concat, [1]]  # cat backbone P4
  - [-1, 3, C2f, [512]]  # 12
  
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
  - [[-1, 4], 1, Concat, [1]]  # cat backbone P3
  - [-1, 3, C2f, [256]]  # 15 (P3/8-small)
  
  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 12], 1, Concat, [1]]  # cat head P4
  - [-1, 3, C2f, [512]]  # 18 (P4/16-medium)
  
  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 9], 1, Concat, [1]]  # cat head P5
  - [-1, 3, C2f, [1024]]  # 21 (P5/32-large)
  
  - [[15, 18, 21], 1, Detect, [1]]  # Detect(P3, P4, P5)

# 锚点配置
anchors:
  # P3/8
  - [10, 13, 16, 30, 33, 23]
  # P4/16  
  - [30, 61, 62, 45, 59, 119]
  # P5/32
  - [116, 90, 156, 198, 373, 326]

# 推理配置
inference:
  # 置信度阈值
  conf_threshold: 0.35
  
  # IoU阈值
  iou_threshold: 0.45
  
  # 最大检测数
  max_detections: 100
  
  # 多标签
  multi_label: false
  
  # 类别无关NMS
  agnostic_nms: false
  
  # 半精度推理
  half_precision: false

# 导出配置
export:
  # 支持的格式
  formats: ["onnx", "torchscript", "tflite", "tensorrt"]
  
  # ONNX配置
  onnx:
    opset_version: 11
    dynamic_axes: true
    
  # TensorRT配置
  tensorrt:
    workspace_size: 4
    fp16: true
    int8: false
    
  # TFLite配置
  tflite:
    quantization: false
    representative_dataset: null

# 性能基准
benchmark:
  # 模型大小 (MB)
  model_size: 14.4
  
  # 参数量 (M)
  parameters: 7.2
  
  # FLOPs (G)
  flops: 21.6
  
  # 推理速度 (ms) - V100
  inference_time:
    v100: 2.8
    rtx3080: 4.2
    cpu: 45.6
