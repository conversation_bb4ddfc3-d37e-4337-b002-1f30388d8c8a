name: alpr_env
channels:
  - pytorch
  - conda-forge
  - defaults
dependencies:
  # Python
  - python=3.9
  
  # 深度学习框架
  - pytorch>=2.0.0
  - torchvision>=0.15.0
  - torchaudio>=2.0.0
  - pytorch-cuda=11.8  # 根据CUDA版本调整
  
  # 计算机视觉
  - opencv-python>=4.8.0
  - pillow>=9.0.0
  - scikit-image>=0.20.0
  
  # 数据处理
  - numpy>=1.21.0
  - pandas>=1.5.0
  - scipy>=1.9.0
  
  # 可视化
  - matplotlib>=3.6.0
  - seaborn>=0.12.0
  - plotly>=5.0.0
  
  # GUI框架
  - pyqt5>=5.15.0
  
  # 工具库
  - tqdm>=4.64.0
  - pyyaml>=6.0
  - requests>=2.28.0
  - pathlib2
  
  # Jupyter相关
  - jupyter
  - ipykernel
  - ipywidgets
  
  # 开发工具
  - pytest>=7.0.0
  - black
  - flake8
  - isort
  
  # pip安装的包
  - pip
  - pip:
    # YOLO相关
    - ultralytics>=8.0.0
    
    # PaddleOCR
    - paddlepaddle-gpu>=2.5.0  # GPU版本，CPU版本使用paddlepaddle
    - paddleocr>=2.7.0
    
    # Fast Plate OCR
    - fast-plate-ocr>=0.1.0
    
    # 其他深度学习工具
    - onnx>=1.14.0
    - onnxruntime-gpu>=1.15.0  # GPU版本，CPU版本使用onnxruntime
    - tensorrt>=8.6.0  # 可选，用于TensorRT推理
    
    # 图像处理增强
    - albumentations>=1.3.0
    - imgaug>=0.4.0
    
    # 评估指标
    - scikit-learn>=1.3.0
    - torchmetrics>=0.11.0
    
    # 配置管理
    - hydra-core>=1.3.0
    - omegaconf>=2.3.0
    
    # 实验跟踪
    - wandb>=0.15.0
    - tensorboard>=2.13.0
    
    # 数据版本控制
    - dvc>=3.0.0
    
    # 模型服务
    - fastapi>=0.100.0
    - uvicorn>=0.23.0
    
    # 其他工具
    - rich>=13.0.0  # 美化终端输出
    - typer>=0.9.0  # CLI工具
    - loguru>=0.7.0  # 日志库
