# Fast Plate OCR车牌识别模型微调配置文件

# 基本配置
basic:
  # 实验名称
  experiment_name: "fastplate_ccpd_finetune"
  
  # 工作目录
  work_dir: "./output/fastplate_rec"
  
  # 随机种子
  seed: 42
  
  # 设备配置
  device: "auto"  # auto, cpu, cuda:0, cuda:1, etc.
  
  # 分布式训练
  distributed: false
  
  # 混合精度训练
  mixed_precision: true

# 数据配置
data:
  # 数据集根目录
  dataset_root: "datasets/CCPD"
  
  # 训练数据
  train_data: "train/rec_gt.txt"
  
  # 验证数据
  val_data: "val/rec_gt.txt"
  
  # 测试数据
  test_data: "test/rec_gt.txt"
  
  # 字符字典
  char_dict: "char_dict.txt"
  
  # 图像配置
  image_config:
    # 输入尺寸 [height, width]
    input_size: [64, 200]
    
    # 通道数
    channels: 3
    
    # 颜色空间
    color_space: "RGB"
    
    # 归一化参数
    normalize:
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]

# 模型配置
model:
  # 基础模型
  base_model: "cct-xs-v1-global-model"
  
  # 模型架构
  architecture:
    # 编码器
    encoder:
      type: "CCT"  # Compact Convolutional Transformer
      
      # 卷积层配置
      conv_layers:
        - filters: 64
          kernel_size: 7
          stride: 2
          padding: 3
        - filters: 128
          kernel_size: 3
          stride: 2
          padding: 1
        - filters: 256
          kernel_size: 3
          stride: 2
          padding: 1
      
      # Transformer配置
      transformer:
        num_layers: 6
        hidden_dim: 256
        num_heads: 8
        mlp_ratio: 4.0
        dropout: 0.1
        attention_dropout: 0.1
    
    # 解码器
    decoder:
      type: "CTC"  # CTC, Attention, or Hybrid
      
      # CTC配置
      ctc:
        blank_index: 0
        
      # 注意力配置
      attention:
        hidden_dim: 256
        num_heads: 8
        max_length: 8
  
  # 损失函数
  loss:
    type: "CTCLoss"
    
    # CTC损失配置
    ctc:
      blank: 0
      reduction: "mean"
      zero_infinity: true
    
    # 交叉熵损失配置
    cross_entropy:
      label_smoothing: 0.1
      ignore_index: -1

# 训练配置
training:
  # 训练轮数
  epochs: 100
  
  # 批次大小
  batch_size: 32
  
  # 验证批次大小
  val_batch_size: 64
  
  # 数据加载器工作进程数
  num_workers: 4
  
  # 优化器
  optimizer:
    type: "AdamW"
    
    # AdamW参数
    adamw:
      lr: 0.001
      betas: [0.9, 0.999]
      eps: 1e-8
      weight_decay: 0.01
      amsgrad: false
    
    # SGD参数
    sgd:
      lr: 0.01
      momentum: 0.9
      weight_decay: 0.0001
      nesterov: true
  
  # 学习率调度器
  lr_scheduler:
    type: "CosineAnnealingLR"
    
    # 余弦退火
    cosine:
      T_max: 100
      eta_min: 1e-6
      
    # 步长调度
    step:
      step_size: 30
      gamma: 0.1
      
    # 多步调度
    multistep:
      milestones: [60, 80]
      gamma: 0.1
      
    # 预热调度
    warmup:
      warmup_epochs: 5
      warmup_factor: 0.1
  
  # 梯度裁剪
  gradient_clipping:
    enabled: true
    max_norm: 1.0
    norm_type: 2
  
  # 早停
  early_stopping:
    enabled: true
    patience: 15
    min_delta: 0.001
    monitor: "val_accuracy"
    mode: "max"

# 数据增强
augmentation:
  # 训练时增强
  train:
    # 几何变换
    geometric:
      # 随机旋转
      random_rotation:
        enabled: true
        degrees: [-5, 5]
        
      # 随机仿射变换
      random_affine:
        enabled: true
        degrees: 5
        translate: [0.1, 0.1]
        scale: [0.9, 1.1]
        shear: 2
        
      # 透视变换
      perspective:
        enabled: true
        distortion_scale: 0.1
        p: 0.3
    
    # 颜色变换
    color:
      # 随机亮度
      brightness:
        enabled: true
        brightness_factor: 0.2
        
      # 随机对比度
      contrast:
        enabled: true
        contrast_factor: 0.2
        
      # 随机饱和度
      saturation:
        enabled: true
        saturation_factor: 0.2
        
      # 随机色调
      hue:
        enabled: true
        hue_factor: 0.1
    
    # 噪声和模糊
    noise:
      # 高斯噪声
      gaussian_noise:
        enabled: true
        std: 0.02
        p: 0.2
        
      # 高斯模糊
      gaussian_blur:
        enabled: true
        kernel_size: [3, 5]
        sigma: [0.1, 2.0]
        p: 0.2
  
  # 验证时增强（通常不使用）
  val:
    enabled: false

# 验证配置
validation:
  # 验证频率
  val_frequency: 1
  
  # 验证指标
  metrics:
    - "accuracy"
    - "edit_distance"
    - "sequence_accuracy"
    - "character_accuracy"
  
  # 保存验证结果
  save_results: true
  
  # 可视化
  visualization:
    enabled: true
    num_samples: 20
    save_path: "vis"

# 保存和加载
checkpoint:
  # 保存频率
  save_frequency: 10
  
  # 保存最佳模型
  save_best: true
  
  # 监控指标
  monitor: "val_accuracy"
  
  # 模式
  mode: "max"
  
  # 保存路径
  save_dir: "checkpoints"
  
  # 恢复训练
  resume: null

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 打印频率
  print_frequency: 10
  
  # TensorBoard
  tensorboard:
    enabled: true
    log_dir: "logs"
    
  # Weights & Biases
  wandb:
    enabled: false
    project: "fastplate-ccpd"
    entity: null

# 推理配置
inference:
  # 批次大小
  batch_size: 1
  
  # 后处理
  postprocess:
    # 置信度阈值
    confidence_threshold: 0.5
    
    # 字符过滤
    character_filter:
      enabled: true
      min_length: 7
      max_length: 8
      allowed_chars: "京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    
    # 格式验证
    format_validation:
      enabled: true
      # 中国车牌格式正则表达式
      pattern: "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4,5}[A-Z0-9挂学警港澳]{1}$"

# 导出配置
export:
  # 导出格式
  formats: ["onnx", "torchscript", "tflite"]
  
  # ONNX导出
  onnx:
    opset_version: 11
    dynamic_axes:
      input: {0: "batch_size", 2: "height", 3: "width"}
      output: {0: "batch_size", 1: "sequence_length"}
    
  # TorchScript导出
  torchscript:
    method: "trace"  # trace or script
    
  # TensorFlow Lite导出
  tflite:
    quantization: false
    representative_dataset_size: 100

# 测试配置
testing:
  # 测试批次大小
  batch_size: 64
  
  # 测试指标
  metrics:
    - "accuracy"
    - "edit_distance"
    - "sequence_accuracy"
    - "character_accuracy"
    - "inference_time"
  
  # 保存测试结果
  save_results: true
  
  # 错误分析
  error_analysis:
    enabled: true
    save_errors: true
    max_errors: 100
